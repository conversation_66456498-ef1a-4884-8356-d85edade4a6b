-- Add video metadata storage for better tracking of individual video data
-- This migration adds support for storing individual video metadata including published dates

-- Create videos table to store individual video metadata
CREATE TABLE IF NOT EXISTS videos (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    youtube_video_id TEXT NOT NULL UNIQUE,
    title TEXT NOT NULL,
    description TEXT,
    published_at TIMESTAMPTZ, -- Actual video publication date
    duration TEXT, -- ISO 8601 duration format (PT4M13S)
    thumbnail_url TEXT,
    view_count BIGINT DEFAULT 0,
    like_count BIGINT DEFAULT 0,
    channel_title TEXT,
    channel_id TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create indexes for video lookups
CREATE INDEX IF NOT EXISTS idx_videos_youtube_id ON videos(youtube_video_id);
CREATE INDEX IF NOT EXISTS idx_videos_published_at ON videos(published_at);
CREATE INDEX IF NOT EXISTS idx_videos_channel_id ON videos(channel_id);

-- Create playlist_videos table to track videos in playlists with position
CREATE TABLE IF NOT EXISTS playlist_videos (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    playlist_id UUID NOT NULL REFERENCES playlists(id) ON DELETE CASCADE,
    video_id UUID NOT NULL REFERENCES videos(id) ON DELETE CASCADE,
    position INTEGER NOT NULL, -- Position in playlist (0-based)
    added_to_playlist_at TIMESTAMPTZ, -- When video was added to playlist
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE(playlist_id, video_id),
    UNIQUE(playlist_id, position)
);

-- Create indexes for playlist_videos
CREATE INDEX IF NOT EXISTS idx_playlist_videos_playlist_id ON playlist_videos(playlist_id);
CREATE INDEX IF NOT EXISTS idx_playlist_videos_video_id ON playlist_videos(video_id);
CREATE INDEX IF NOT EXISTS idx_playlist_videos_position ON playlist_videos(playlist_id, position);
CREATE INDEX IF NOT EXISTS idx_playlist_videos_active ON playlist_videos(is_active);

-- Add triggers for updated_at timestamps
CREATE TRIGGER set_timestamp_videos
    BEFORE UPDATE ON videos
    FOR EACH ROW
    EXECUTE FUNCTION trigger_set_timestamp();

CREATE TRIGGER set_timestamp_playlist_videos
    BEFORE UPDATE ON playlist_videos
    FOR EACH ROW
    EXECUTE FUNCTION trigger_set_timestamp();

-- Create view for playlist videos with metadata
CREATE OR REPLACE VIEW playlist_videos_with_metadata AS
SELECT 
    pv.playlist_id,
    pv.position,
    pv.added_to_playlist_at,
    pv.is_active as playlist_video_active,
    v.id as video_id,
    v.youtube_video_id,
    v.title,
    v.description,
    v.published_at,
    v.duration,
    v.thumbnail_url,
    v.view_count,
    v.like_count,
    v.channel_title,
    v.channel_id,
    p.title as playlist_title,
    p.youtube_playlist_id
FROM playlist_videos pv
JOIN videos v ON pv.video_id = v.id
JOIN playlists p ON pv.playlist_id = p.id
WHERE pv.is_active = true
ORDER BY pv.playlist_id, pv.position;

-- Update playlists table to have a computed video count
-- Add a function to update video count automatically
CREATE OR REPLACE FUNCTION update_playlist_video_count()
RETURNS TRIGGER AS $$
BEGIN
    -- Update the video count for the affected playlist
    UPDATE playlists 
    SET video_count = (
        SELECT COUNT(*) 
        FROM playlist_videos 
        WHERE playlist_id = COALESCE(NEW.playlist_id, OLD.playlist_id) 
        AND is_active = true
    ),
    updated_at = NOW()
    WHERE id = COALESCE(NEW.playlist_id, OLD.playlist_id);
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Create triggers to automatically update video count
CREATE TRIGGER update_playlist_count_on_insert
    AFTER INSERT ON playlist_videos
    FOR EACH ROW
    EXECUTE FUNCTION update_playlist_video_count();

CREATE TRIGGER update_playlist_count_on_update
    AFTER UPDATE ON playlist_videos
    FOR EACH ROW
    EXECUTE FUNCTION update_playlist_video_count();

CREATE TRIGGER update_playlist_count_on_delete
    AFTER DELETE ON playlist_videos
    FOR EACH ROW
    EXECUTE FUNCTION update_playlist_video_count();
