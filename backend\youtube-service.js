const { google } = require('googleapis');
const db = require('./db');

class YouTubeService {
    constructor() {
        this.youtube = google.youtube('v3');
    }

    /**
     * Get authenticated YouTube client for a user
     */
    async getAuthenticatedClient(userId) {
        try {
            const result = await db.query(
                `SELECT youtube_api_key, youtube_client_id, youtube_client_secret,
                        youtube_refresh_token, youtube_access_token, youtube_token_expiry
                 FROM users WHERE id = $1`,
                [userId]
            );

            if (result.rowCount === 0) {
                throw new Error('User not found');
            }

            const user = result.rows[0];
            const { youtube_api_key, youtube_client_id, youtube_client_secret,
                    youtube_refresh_token, youtube_access_token, youtube_token_expiry } = user;

            if (!youtube_api_key) {
                throw new Error('YouTube API key not configured');
            }

            // For basic operations (public playlists), we can use the API key
            const client = google.youtube({
                version: 'v3',
                auth: youtube_api_key
            });

            // For private/unlisted content, we need OAuth2
            if (youtube_client_id && youtube_client_secret && youtube_refresh_token) {
                const oauth2Client = new google.auth.OAuth2(
                    youtube_client_id,
                    youtube_client_secret
                );

                oauth2Client.setCredentials({
                    refresh_token: youtube_refresh_token,
                    access_token: youtube_access_token,
                    expiry_date: youtube_token_expiry ? new Date(youtube_token_expiry).getTime() : null
                });

                // Refresh token if expired
                if (!youtube_access_token || (youtube_token_expiry && new Date(youtube_token_expiry) <= new Date())) {
                    const tokens = await oauth2Client.refreshAccessToken();
                    const newCredentials = tokens.credentials;

                    // Update database with new tokens
                    await db.query(
                        `UPDATE users SET
                            youtube_access_token = $1,
                            youtube_refresh_token = $2,
                            youtube_token_expiry = $3
                         WHERE id = $4`,
                        [
                            newCredentials.access_token,
                            newCredentials.refresh_token,
                            new Date(newCredentials.expiry_date),
                            userId
                        ]
                    );

                    oauth2Client.setCredentials(newCredentials);
                }

                return google.youtube({
                    version: 'v3',
                    auth: oauth2Client
                });
            }

            return client;
        } catch (error) {
            console.error('Error getting authenticated YouTube client:', error);
            throw error;
        }
    }

    /**
     * Get user's YouTube channel information
     */
    async getChannelInfo(userId) {
        try {
            const client = await this.getAuthenticatedClient(userId);

            // First get user's channel ID if not stored
            let channelId;
            const userResult = await db.query('SELECT youtube_channel_id FROM users WHERE id = $1', [userId]);

            if (userResult.rows[0].youtube_channel_id) {
                channelId = userResult.rows[0].youtube_channel_id;
            } else {
                // Get channel info from authenticated user
                const channelsResponse = await client.channels.list({
                    part: 'id,snippet',
                    mine: true
                });

                if (channelsResponse.data.items && channelsResponse.data.items.length > 0) {
                    channelId = channelsResponse.data.items[0].id;

                    // Store channel ID in database
                    await db.query('UPDATE users SET youtube_channel_id = $1 WHERE id = $2', [channelId, userId]);
                }
            }

            if (!channelId) {
                throw new Error('Could not determine YouTube channel ID');
            }

            // Get detailed channel information
            const channelResponse = await client.channels.list({
                part: 'snippet,statistics',
                id: channelId
            });

            return channelResponse.data.items[0];
        } catch (error) {
            console.error('Error getting channel info:', error);
            throw error;
        }
    }

    /**
     * Get playlists from user's channel
     */
    async getChannelPlaylists(userId) {
        try {
            const client = await this.getAuthenticatedClient(userId);
            const userResult = await db.query('SELECT youtube_channel_id FROM users WHERE id = $1', [userId]);

            if (!userResult.rows[0].youtube_channel_id) {
                throw new Error('YouTube channel ID not configured');
            }

            const channelId = userResult.rows[0].youtube_channel_id;

            const response = await client.playlists.list({
                part: 'snippet,status,contentDetails',
                channelId: channelId,
                maxResults: 50
            });

            return response.data.items || [];
        } catch (error) {
            console.error('Error getting channel playlists:', error);
            throw error;
        }
    }

    /**
     * Get videos from a specific playlist
     */
    async getPlaylistVideos(userId, playlistId) {
        try {
            const client = await this.getAuthenticatedClient(userId);

            // First, get playlist items
            const playlistResponse = await client.playlistItems.list({
                part: 'snippet,contentDetails,status',
                playlistId: playlistId,
                maxResults: 50
            });

            const playlistItems = playlistResponse.data.items || [];

            if (playlistItems.length === 0) {
                return [];
            }

            // Extract video IDs from playlist items
            const videoIds = playlistItems
                .map(item => item.snippet?.resourceId?.videoId || item.contentDetails?.videoId)
                .filter(id => id);

            if (videoIds.length === 0) {
                return playlistItems; // Return original data if no video IDs found
            }

            // Fetch detailed video information including actual published dates
            const videosResponse = await client.videos.list({
                part: 'snippet,contentDetails,statistics',
                id: videoIds.join(','),
                maxResults: 50
            });

            const videoDetails = videosResponse.data.items || [];

            // Merge playlist item data with detailed video data
            const enrichedVideos = playlistItems.map(playlistItem => {
                const videoId = playlistItem.snippet?.resourceId?.videoId || playlistItem.contentDetails?.videoId;
                const videoDetail = videoDetails.find(video => video.id === videoId);

                return {
                    ...playlistItem,
                    // Add enriched video data
                    videoDetails: videoDetail ? {
                        id: videoDetail.id,
                        snippet: {
                            ...videoDetail.snippet,
                            // Keep the actual video published date
                            actualPublishedAt: videoDetail.snippet.publishedAt,
                            // Keep playlist position info from original
                            playlistPublishedAt: playlistItem.snippet.publishedAt,
                            position: playlistItem.snippet.position
                        },
                        contentDetails: videoDetail.contentDetails,
                        statistics: videoDetail.statistics
                    } : null
                };
            });

            return enrichedVideos;
        } catch (error) {
            console.error('Error getting playlist videos:', error);
            throw error;
        }
    }

    /**
     * Get videos from a public playlist (TEST - for development without full credentials)
     * TODO: Remove this method when proper YouTube authentication is implemented
     */
    async getPublicPlaylistVideos(playlistId, apiKey = null) {
        try {
            // TEST: Use provided API key or fallback to environment variable
            const key = apiKey || process.env.YOUTUBE_API_KEY;

            if (!key) {
                throw new Error('YouTube API key not available for public playlist access');
            }

            const client = google.youtube({
                version: 'v3',
                auth: key
            });

            // First, get playlist items
            const playlistResponse = await client.playlistItems.list({
                part: 'snippet,contentDetails,status',
                playlistId: playlistId,
                maxResults: 50
            });

            const playlistItems = playlistResponse.data.items || [];

            if (playlistItems.length === 0) {
                return [];
            }

            // Extract video IDs from playlist items
            const videoIds = playlistItems
                .map(item => item.snippet?.resourceId?.videoId || item.contentDetails?.videoId)
                .filter(id => id);

            if (videoIds.length === 0) {
                return playlistItems; // Return original data if no video IDs found
            }

            // Fetch detailed video information including actual published dates
            const videosResponse = await client.videos.list({
                part: 'snippet,contentDetails,statistics',
                id: videoIds.join(','),
                maxResults: 50
            });

            const videoDetails = videosResponse.data.items || [];

            // Merge playlist item data with detailed video data
            const enrichedVideos = playlistItems.map(playlistItem => {
                const videoId = playlistItem.snippet?.resourceId?.videoId || playlistItem.contentDetails?.videoId;
                const videoDetail = videoDetails.find(video => video.id === videoId);

                return {
                    ...playlistItem,
                    // Add enriched video data
                    videoDetails: videoDetail ? {
                        id: videoDetail.id,
                        snippet: {
                            ...videoDetail.snippet,
                            // Keep the actual video published date
                            actualPublishedAt: videoDetail.snippet.publishedAt,
                            // Keep playlist position info from original
                            playlistPublishedAt: playlistItem.snippet.publishedAt,
                            position: playlistItem.snippet.position
                        },
                        contentDetails: videoDetail.contentDetails,
                        statistics: videoDetail.statistics
                    } : null
                };
            });

            return enrichedVideos;
        } catch (error) {
            console.error('Error getting public playlist videos:', error);
            throw error;
        }
    }

    /**
     * Get public playlist details (TEST - for development without full credentials)
     */
    async getPublicPlaylistDetails(playlistId, apiKey = null) {
        try {
            // TEST: Use provided API key or fallback to environment variable
            const key = apiKey || process.env.YOUTUBE_API_KEY;

            if (!key) {
                throw new Error('YouTube API key not available for public playlist access');
            }

            const client = google.youtube({
                version: 'v3',
                auth: key
            });

            // Get playlist details and actual video count
            const [playlistResponse, videosResponse] = await Promise.all([
                client.playlists.list({
                    part: 'snippet,status,contentDetails',
                    id: playlistId
                }),
                client.playlistItems.list({
                    part: 'snippet',
                    playlistId: playlistId,
                    maxResults: 50
                })
            ]);

            if (!playlistResponse.data.items || playlistResponse.data.items.length === 0) {
                throw new Error('Public playlist not found');
            }

            const playlist = playlistResponse.data.items[0];
            const actualVideoCount = videosResponse.data.items ? videosResponse.data.items.length : 0;

            // Update the playlist with accurate video count
            return {
                ...playlist,
                contentDetails: {
                    ...playlist.contentDetails,
                    itemCount: actualVideoCount,
                    actualItemCount: actualVideoCount
                }
            };
        } catch (error) {
            console.error('Error getting public playlist details:', error);
            throw error;
        }
    }

    /**
     * Sync a YouTube playlist as a course
     */
    async syncPlaylistAsCourse(userId, playlistId, customTitle = null, customDescription = null) {
        try {
            const client = await this.getAuthenticatedClient(userId);

            // Get playlist details
            const playlistResponse = await client.playlists.list({
                part: 'snippet,status,contentDetails',
                id: playlistId
            });

            if (!playlistResponse.data.items || playlistResponse.data.items.length === 0) {
                throw new Error('Playlist not found');
            }

            const playlist = playlistResponse.data.items[0];

            // Get actual video count by fetching playlist items
            const videosResponse = await client.playlistItems.list({
                part: 'snippet',
                playlistId: playlistId,
                maxResults: 50
            });
            const videoCount = videosResponse.data.items ? videosResponse.data.items.length : 0;

            // Create or update course
            const courseData = {
                youtube_playlist_id: playlistId,
                youtube_playlist_url: `https://www.youtube.com/playlist?list=${playlistId}`,
                youtube_channel_title: playlist.snippet.channelTitle,
                youtube_video_count: videoCount,
                youtube_thumbnail_url: playlist.snippet.thumbnails?.default?.url,
                synced_at: new Date(),
                title: customTitle || playlist.snippet.title,
                description: customDescription || playlist.snippet.description,
                slug: this.generateSlug(customTitle || playlist.snippet.title)
            };

            // Check if course already exists
            const existingCourse = await db.query(
                'SELECT id FROM courses WHERE youtube_playlist_id = $1',
                [playlistId]
            );

            let courseId;
            if (existingCourse.rowCount > 0) {
                // Update existing course
                courseId = existingCourse.rows[0].id;
                await db.query(
                    `UPDATE courses SET
                        title = $1,
                        description = $2,
                        youtube_channel_title = $3,
                        youtube_video_count = $4,
                        youtube_thumbnail_url = $5,
                        synced_at = $6,
                        updated_at = NOW()
                     WHERE id = $7`,
                    [
                        courseData.title,
                        courseData.description,
                        courseData.youtube_channel_title,
                        courseData.youtube_video_count,
                        courseData.youtube_thumbnail_url,
                        courseData.synced_at,
                        courseId
                    ]
                );
            } else {
                // Create new course
                const result = await db.query(
                    `INSERT INTO courses (
                        slug, title, description, youtube_playlist_id, youtube_playlist_url,
                        youtube_channel_title, youtube_video_count, youtube_thumbnail_url, synced_at
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9) RETURNING id`,
                    [
                        courseData.slug,
                        courseData.title,
                        courseData.description,
                        courseData.youtube_playlist_id,
                        courseData.youtube_playlist_url,
                        courseData.youtube_channel_title,
                        courseData.youtube_video_count,
                        courseData.youtube_thumbnail_url,
                        courseData.synced_at
                    ]
                );
                courseId = result.rows[0].id;
            }

            // Sync video metadata if this is a playlist-based course
            if (courseData.youtube_playlist_id) {
                try {
                    // Find the playlist in our playlists table
                    const playlistResult = await db.query(
                        'SELECT id FROM playlists WHERE youtube_playlist_id = $1',
                        [courseData.youtube_playlist_id]
                    );

                    if (playlistResult.rowCount > 0) {
                        const playlistDbId = playlistResult.rows[0].id;
                        await this.syncPlaylistVideos(playlistDbId, courseData.youtube_playlist_id);
                    }
                } catch (syncError) {
                    console.warn('Warning: Could not sync video metadata:', syncError.message);
                    // Don't fail the entire sync if video metadata sync fails
                }
            }

            return { courseId, ...courseData };
        } catch (error) {
            console.error('Error syncing playlist as course:', error);
            throw error;
        }
    }

    /**
     * Sync playlist videos to database with metadata
     */
    async syncPlaylistVideos(playlistId, youtubePlaylistId, apiKey = null) {
        try {
            // Get videos with enriched metadata
            const videos = await this.getPublicPlaylistVideos(youtubePlaylistId, apiKey);

            if (!videos || videos.length === 0) {
                console.log('No videos found in playlist');
                return;
            }

            // Process each video
            for (const video of videos) {
                const videoDetails = video.videoDetails;
                if (!videoDetails) continue;

                const youtubeVideoId = videoDetails.id;
                const position = video.snippet?.position || 0;

                // Insert or update video metadata
                const videoResult = await db.query(
                    `INSERT INTO videos (
                        youtube_video_id, title, description, published_at, duration,
                        thumbnail_url, view_count, like_count, channel_title, channel_id
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
                    ON CONFLICT (youtube_video_id)
                    DO UPDATE SET
                        title = EXCLUDED.title,
                        description = EXCLUDED.description,
                        published_at = EXCLUDED.published_at,
                        duration = EXCLUDED.duration,
                        thumbnail_url = EXCLUDED.thumbnail_url,
                        view_count = EXCLUDED.view_count,
                        like_count = EXCLUDED.like_count,
                        channel_title = EXCLUDED.channel_title,
                        channel_id = EXCLUDED.channel_id,
                        updated_at = NOW()
                    RETURNING id`,
                    [
                        youtubeVideoId,
                        videoDetails.snippet.title,
                        videoDetails.snippet.description,
                        videoDetails.snippet.actualPublishedAt || videoDetails.snippet.publishedAt,
                        videoDetails.contentDetails?.duration,
                        videoDetails.snippet.thumbnails?.medium?.url || videoDetails.snippet.thumbnails?.high?.url,
                        parseInt(videoDetails.statistics?.viewCount || 0),
                        parseInt(videoDetails.statistics?.likeCount || 0),
                        videoDetails.snippet.channelTitle,
                        videoDetails.snippet.channelId
                    ]
                );

                const videoId = videoResult.rows[0].id;

                // Insert or update playlist-video relationship
                await db.query(
                    `INSERT INTO playlist_videos (
                        playlist_id, video_id, position, added_to_playlist_at
                    ) VALUES ($1, $2, $3, $4)
                    ON CONFLICT (playlist_id, video_id)
                    DO UPDATE SET
                        position = EXCLUDED.position,
                        added_to_playlist_at = EXCLUDED.added_to_playlist_at,
                        is_active = true,
                        updated_at = NOW()`,
                    [
                        playlistId,
                        videoId,
                        position,
                        videoDetails.snippet.playlistPublishedAt || video.snippet?.publishedAt
                    ]
                );
            }

            console.log(`Synced ${videos.length} videos for playlist ${youtubePlaylistId}`);
        } catch (error) {
            console.error('Error syncing playlist videos:', error);
            throw error;
        }
    }

    /**
     * Sync playlist metadata and videos from YouTube
     */
    async syncPlaylistMetadata(youtubePlaylistId, apiKey = null) {
        try {
            // Get playlist details with accurate video count
            const playlistDetails = await this.getPublicPlaylistDetails(youtubePlaylistId, apiKey);

            // Insert or update playlist in database
            const playlistResult = await db.query(
                `INSERT INTO playlists (
                    youtube_playlist_id, title, description, thumbnail_url, video_count
                ) VALUES ($1, $2, $3, $4, $5)
                ON CONFLICT (youtube_playlist_id)
                DO UPDATE SET
                    title = EXCLUDED.title,
                    description = EXCLUDED.description,
                    thumbnail_url = EXCLUDED.thumbnail_url,
                    video_count = EXCLUDED.video_count,
                    updated_at = NOW()
                RETURNING id`,
                [
                    youtubePlaylistId,
                    playlistDetails.snippet.title,
                    playlistDetails.snippet.description,
                    playlistDetails.snippet.thumbnails?.medium?.url || playlistDetails.snippet.thumbnails?.high?.url,
                    playlistDetails.contentDetails.actualItemCount || playlistDetails.contentDetails.itemCount
                ]
            );

            const playlistDbId = playlistResult.rows[0].id;

            // Sync video metadata
            await this.syncPlaylistVideos(playlistDbId, youtubePlaylistId, apiKey);

            return {
                playlistId: playlistDbId,
                youtubePlaylistId,
                title: playlistDetails.snippet.title,
                videoCount: playlistDetails.contentDetails.actualItemCount || playlistDetails.contentDetails.itemCount
            };
        } catch (error) {
            console.error('Error syncing playlist metadata:', error);
            throw error;
        }
    }

    /**
     * Generate URL-friendly slug from title
     */
    generateSlug(title) {
        return title
            .toLowerCase()
            .replace(/[^\w\s-]/g, '') // Remove special characters
            .replace(/[\s_-]+/g, '-') // Replace spaces and underscores with hyphens
            .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
    }
}

module.exports = new YouTubeService();
