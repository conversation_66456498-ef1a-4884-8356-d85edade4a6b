import React, { useState, useEffect } from 'react';
import AdminDashboardHeader from '../../components/Layout/AdminDashboardHeader';
import Footer from '../../components/Layout/Footer';
import api from '../../services/api';
import PlaylistManagement from './PlaylistManagement';
// import YouTubeSettings from './YouTubeSettings'; // Commented out for simplification
import {
  AdminDashboardContainer,
  Main,
  MainHeader,
  MainTitle,
  MainContent,
  StudentList,
  StudentItem,
  StudentInfo,
  StudentName,
  StudentEmail,
  StudentActions,
  ActionButton,
  LoadingText,
  ErrorText,
  EmptyText,
  AddStudentButton,
  Modal,
  ModalContent,
  ModalHeader,
  ModalTitle,
  ModalBody,
  FormGroup,
  Label,
  Input,
  Textarea,
  ModalFooter,
  CancelButton,
  SubmitButton
} from './AdminDashboard.styles';

const AdminDashboard = () => {
  const [activeTab, setActiveTab] = useState('students');
  const [students, setStudents] = useState([]);
  const [filteredStudents, setFilteredStudents] = useState([]);
  const [playlists, setPlaylists] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showPlaylistModal, setShowPlaylistModal] = useState(false);
  const [showMessageModal, setShowMessageModal] = useState(false);
  const [selectedStudent, setSelectedStudent] = useState(null);
  const [studentPlaylists, setStudentPlaylists] = useState([]);
  const [message, setMessage] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [newStudent, setNewStudent] = useState({
    email: '',
    password: '',
    firstName: '',
    lastName: ''
  });

  useEffect(() => {
    loadData();
  }, []);

  useEffect(() => {
    // Filter students based on search query
    if (!searchQuery.trim()) {
      setFilteredStudents(students);
    } else {
      const filtered = students.filter(student =>
        `${student.first_name} ${student.last_name}`.toLowerCase().includes(searchQuery.toLowerCase()) ||
        student.email.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredStudents(filtered);
    }
  }, [students, searchQuery]);

  const loadData = async () => {
    try {
      setLoading(true);
      const [studentsResponse, playlistsResponse] = await Promise.all([
        api.get('/api/admin/users'),
        api.get('/api/admin/playlists')
      ]);

      console.log('=== LOAD DATA DEBUG ===');
      console.log('Raw students response:', studentsResponse.data);
      console.log('Raw playlists response:', playlistsResponse.data);

      // Filter out admin users to show only students
      const studentUsers = studentsResponse.data.filter(user => user.role !== 'admin');
      console.log('Filtered student users:', studentUsers);
      console.log('Sample student object:', studentUsers[0]);

      setStudents(studentUsers);
      setFilteredStudents(studentUsers); // Initially show all students
      setPlaylists(playlistsResponse.data);
      setError(null);
      console.log('=== LOAD DATA SUCCESS ===');

      // Auto-sync playlists that need updating (in background)
      autoSyncPlaylistsIfNeeded(playlistsResponse.data);
    } catch (err) {
      console.error('Error loading data:', err);
      setError('Error cargando datos');
    } finally {
      setLoading(false);
    }
  };

  const loadStudentPlaylists = async (studentId) => {
    try {
      // Get all playlists and check which ones the student has access to
      const [allPlaylistsResponse, studentAccessResponse] = await Promise.all([
        api.get('/api/admin/playlists'),
        // We'll need to check each playlist individually or create a custom endpoint
        // For now, let's get all playlists and then check access for each
        Promise.resolve({ data: [] }) // Placeholder - we'll populate this below
      ]);

      // Get student's accessible playlists by checking each playlist
      const studentPlaylistsPromises = allPlaylistsResponse.data.map(async (playlist) => {
        try {
          const detailResponse = await api.get(`/api/admin/playlists/${playlist.playlist_id}`);
          const hasAccess = detailResponse.data.users.some(user => user.user_id === studentId && user.is_active);
          return hasAccess ? { playlist_id: playlist.playlist_id, ...playlist } : null;
        } catch (err) {
          return null;
        }
      });

      const studentPlaylistsResults = await Promise.all(studentPlaylistsPromises);
      const studentPlaylists = studentPlaylistsResults.filter(playlist => playlist !== null);

      setStudentPlaylists(studentPlaylists);
    } catch (err) {
      console.error('Error loading student playlists:', err);
      setStudentPlaylists([]);
    }
  };

  const handleSearch = (query) => {
    setSearchQuery(query);
  };

  const handleAddStudent = async (e) => {
    e.preventDefault();
    try {
      await api.post('/api/admin/create-user', newStudent);
      setShowAddModal(false);
      setNewStudent({ email: '', password: '', firstName: '', lastName: '' });
      loadData(); // Reload the list
    } catch (err) {
      console.error('Error adding student:', err);
      setError('Error agregando estudiante');
    }
  };

  const handleRemoveStudent = async (studentId) => {
    if (!window.confirm('¿Estás seguro de que quieres eliminar este estudiante?')) {
      return;
    }
    try {
      await api.delete(`/api/admin/users/${studentId}`);
      loadData(); // Reload the list
    } catch (err) {
      console.error('Error removing student:', err);
      setError('Error eliminando estudiante');
    }
  };

  const handleManagePlaylists = async (student) => {
    setSelectedStudent(student);
    await loadStudentPlaylists(student.id);
    setShowPlaylistModal(true);
  };

  const handleTogglePlaylistAccess = async (playlistId, hasAccess) => {
    if (!selectedStudent) return;

    try {
      // Enhanced debugging information
      console.log('=== TOGGLE PLAYLIST ACCESS DEBUG ===');
      console.log('Selected Student Object:', selectedStudent);
      console.log('Playlist ID:', playlistId);
      console.log('Student ID:', selectedStudent.id);
      console.log('Student ID Type:', typeof selectedStudent.id);
      console.log('Playlist ID Type:', typeof playlistId);
      console.log('Has Access:', hasAccess);
      console.log('Action:', hasAccess ? 'revoke' : 'grant');

      // Validate IDs before making request
      if (!selectedStudent.id) {
        throw new Error('Student ID is missing or undefined');
      }
      if (!playlistId) {
        throw new Error('Playlist ID is missing or undefined');
      }

      // Check if IDs are valid UUIDs (basic validation)
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
      if (!uuidRegex.test(selectedStudent.id)) {
        console.error('Invalid student ID format:', selectedStudent.id);
        throw new Error(`Invalid student ID format: ${selectedStudent.id}`);
      }
      if (!uuidRegex.test(playlistId)) {
        console.error('Invalid playlist ID format:', playlistId);
        throw new Error(`Invalid playlist ID format: ${playlistId}`);
      }

      const endpoint = `/api/admin/playlists/${playlistId}/users/${selectedStudent.id}`;
      console.log('API Endpoint:', endpoint);

      if (hasAccess) {
        // Revoke access
        console.log('Making DELETE request to:', endpoint);
        await api.delete(endpoint);
      } else {
        // Grant access
        console.log('Making POST request to:', endpoint);
        await api.post(endpoint);
      }

      console.log('API request successful, reloading student playlists...');
      // Reload student's playlists
      await loadStudentPlaylists(selectedStudent.id);
      console.log('=== TOGGLE PLAYLIST ACCESS SUCCESS ===');
    } catch (err) {
      console.error('=== TOGGLE PLAYLIST ACCESS ERROR ===');
      console.error('Error toggling playlist access:', err);
      console.error('Error details:', {
        message: err.message,
        response: err.response?.data,
        status: err.response?.status,
        config: {
          method: err.config?.method,
          url: err.config?.url,
          baseURL: err.config?.baseURL
        }
      });

      // More detailed error message
      let errorMessage = 'Error cambiando acceso a la playlist';
      if (err.response?.status === 500) {
        errorMessage += ' (Error interno del servidor)';
      } else if (err.response?.status === 404) {
        errorMessage += ' (Usuario o playlist no encontrado)';
      } else if (err.response?.status === 400) {
        errorMessage += ' (Datos inválidos)';
      }
      errorMessage += `: ${err.response?.data?.error || err.message}`;

      setError(errorMessage);
      console.error('=== END ERROR DEBUG ===');
    }
  };

  const handleSendMessage = async (student) => {
    setSelectedStudent(student);
    setMessage('');
    setShowMessageModal(true);
  };

  const autoSyncPlaylistsIfNeeded = async (playlistsData) => {
    try {
      const playlistsToSync = playlistsData.filter(playlist => {
        // Sync if video_count is null, 0, or playlist was updated more than 24 hours ago
        const needsSync = !playlist.video_count ||
                         playlist.video_count === 0 ||
                         !playlist.updated_at ||
                         (new Date() - new Date(playlist.updated_at)) > 24 * 60 * 60 * 1000; // 24 hours

        return needsSync;
      });

      if (playlistsToSync.length === 0) {
        console.log('All playlists are up to date');
        return;
      }

      console.log(`Auto-syncing ${playlistsToSync.length} playlists in background...`);

      // Sync playlists in background (limit to 2 at a time to avoid overwhelming the API)
      const syncPromises = playlistsToSync.slice(0, 2).map(async (playlist) => {
        try {
          console.log(`Background syncing playlist: ${playlist.youtube_playlist_id}`);
          const response = await api.post(`/api/admin/playlists/${playlist.youtube_playlist_id}/sync`);
          console.log(`Background sync completed for: ${playlist.title}`, response.data);
          return { success: true, playlist: playlist.youtube_playlist_id };
        } catch (err) {
          console.warn(`Background sync failed for ${playlist.title}:`, err.message);
          return { success: false, playlist: playlist.youtube_playlist_id, error: err.message };
        }
      });

      // Don't await - let it run in background
      Promise.allSettled(syncPromises).then(async (results) => {
        const successfulSyncs = results.filter(r => r.status === 'fulfilled' && r.value.success);
        console.log(`Background playlist sync completed. ${successfulSyncs.length}/${results.length} successful`);

        // Refresh data after background sync if any were successful
        if (successfulSyncs.length > 0) {
          try {
            await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds
            const response = await api.get('/api/admin/playlists');
            setPlaylists(response.data);
            console.log('Playlists refreshed after background sync');
          } catch (err) {
            console.warn('Failed to refresh playlists after background sync:', err);
          }
        }
      });

    } catch (err) {
      console.error('Error in background auto-sync:', err);
      // Don't show error to user for background sync failures
    }
  };

  const handleSendMessageSubmit = async (e) => {
    e.preventDefault();
    if (!message.trim()) return;

    try {
      // TODO: Implement backend messaging endpoint
      alert(`Mensaje enviado a ${selectedStudent.email}: ${message}`);
      setShowMessageModal(false);
      setMessage('');
    } catch (err) {
      console.error('Error sending message:', err);
      setError('Error enviando mensaje');
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setNewStudent(prev => ({
      ...prev,
      [name]: value
    }));
  };

  return (
    <AdminDashboardContainer>
      <AdminDashboardHeader
        searchQuery={searchQuery}
        setSearchQuery={setSearchQuery}
        handleSearch={handleSearch}
      />
      <Main>
        <MainHeader>
          <MainTitle>Panel de Administración</MainTitle>
          <div style={{ display: 'flex', gap: '1rem', marginBottom: '1rem' }}>
            <button
              onClick={() => setActiveTab('students')}
              style={{
                padding: '0.5rem 1rem',
                backgroundColor: activeTab === 'students' ? 'var(--color-primary)' : 'var(--color-secondary)',
                color: activeTab === 'students' ? 'white' : 'var(--color-text)',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              Estudiantes
            </button>
            <button
              onClick={() => setActiveTab('playlists')}
              style={{
                padding: '0.5rem 1rem',
                backgroundColor: activeTab === 'playlists' ? 'var(--color-primary)' : 'var(--color-secondary)',
                color: activeTab === 'playlists' ? 'white' : 'var(--color-text)',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              Playlists
            </button>
            <button
              onClick={() => setActiveTab('youtube')}
              style={{
                padding: '0.5rem 1rem',
                backgroundColor: activeTab === 'youtube' ? 'var(--color-primary)' : 'var(--color-secondary)',
                color: activeTab === 'youtube' ? 'white' : 'var(--color-text)',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              YouTube
            </button>
          </div>
          {activeTab === 'students' && (
            <AddStudentButton onClick={() => setShowAddModal(true)}>
              Agregar Estudiante
            </AddStudentButton>
          )}
        </MainHeader>
        <MainContent>
          {activeTab === 'students' && (
            <>
              {loading && <LoadingText>Cargando estudiantes...</LoadingText>}
              {error && <ErrorText>Error: {error}</ErrorText>}
              {!loading && !error && students.length === 0 && (
                <EmptyText>No hay estudiantes registrados</EmptyText>
              )}
              {!loading && !error && students.length > 0 && filteredStudents.length === 0 && (
                <EmptyText>No se encontraron estudiantes con esa búsqueda</EmptyText>
              )}
              {!loading && !error && filteredStudents.length > 0 && (
                <StudentList>
                  {filteredStudents.map((student) => (
                    <StudentItem key={student.id}>
                      <StudentInfo>
                        <StudentName>
                          {student.first_name && student.last_name
                            ? `${student.first_name} ${student.last_name}`
                            : 'Sin nombre'}
                        </StudentName>
                        <StudentEmail>{student.email}</StudentEmail>
                      </StudentInfo>
                      <StudentActions>
                        <ActionButton onClick={() => handleManagePlaylists(student)}>
                          Gestionar Playlists
                        </ActionButton>
                        <ActionButton onClick={() => handleSendMessage(student)}>
                          Enviar Mensaje
                        </ActionButton>
                        <ActionButton
                          onClick={() => handleRemoveStudent(student.id)}
                          danger
                        >
                          Eliminar
                        </ActionButton>
                      </StudentActions>
                    </StudentItem>
                  ))}
                </StudentList>
              )}
            </>
          )}



          {activeTab === 'playlists' && (
            <PlaylistManagement />
          )}

          {activeTab === 'youtube' && (
            <div style={{ padding: '2rem', textAlign: 'center' }}>
              <h3>Configuración de YouTube</h3>
              <p style={{
                fontSize: 'var(--font-size-lg)',
                color: 'var(--color-text-secondary)',
                marginTop: '2rem'
              }}>
                La configuración de YouTube ha sido simplificada temporalmente.
              </p>
              <p style={{
                fontSize: 'var(--font-size-sm)',
                color: 'var(--color-text-secondary)',
                marginTop: '1rem'
              }}>
                Para importar nuevas playlists de YouTube, contacta al administrador del sistema.
              </p>
            </div>
          )}
        </MainContent>
      </Main>
      <Footer />

      {showAddModal && (
        <Modal>
          <ModalContent>
            <ModalHeader>
              <ModalTitle>Agregar Nuevo Estudiante</ModalTitle>
            </ModalHeader>
            <form onSubmit={handleAddStudent}>
              <ModalBody>
                <FormGroup>
                  <Label htmlFor="firstName">Nombre</Label>
                  <Input
                    type="text"
                    id="firstName"
                    name="firstName"
                    value={newStudent.firstName}
                    onChange={handleInputChange}
                    required
                  />
                </FormGroup>
                <FormGroup>
                  <Label htmlFor="lastName">Apellido</Label>
                  <Input
                    type="text"
                    id="lastName"
                    name="lastName"
                    value={newStudent.lastName}
                    onChange={handleInputChange}
                    required
                  />
                </FormGroup>
                <FormGroup>
                  <Label htmlFor="email">Email</Label>
                  <Input
                    type="email"
                    id="email"
                    name="email"
                    value={newStudent.email}
                    onChange={handleInputChange}
                    required
                  />
                </FormGroup>
                <FormGroup>
                  <Label htmlFor="password">Contraseña</Label>
                  <Input
                    type="password"
                    id="password"
                    name="password"
                    value={newStudent.password}
                    onChange={handleInputChange}
                    required
                  />
                </FormGroup>
              </ModalBody>
              <ModalFooter>
                <CancelButton type="button" onClick={() => setShowAddModal(false)}>
                  Cancelar
                </CancelButton>
                <SubmitButton type="submit">
                  Agregar Estudiante
                </SubmitButton>
              </ModalFooter>
            </form>
          </ModalContent>
        </Modal>
      )}

      {showPlaylistModal && selectedStudent && (
        <Modal>
          <ModalContent style={{
            maxWidth: '700px',
            width: '90vw',
            maxHeight: '90vh',
            display: 'flex',
            flexDirection: 'column'
          }}>
            <ModalHeader style={{ flexShrink: '0' }}>
              <ModalTitle style={{
                fontSize: 'var(--font-size-lg)',
                wordBreak: 'break-word',
                lineHeight: '1.3'
              }}>
                Gestionar Playlists - {selectedStudent.first_name && selectedStudent.last_name
                  ? `${selectedStudent.first_name} ${selectedStudent.last_name}`
                  : selectedStudent.email}
              </ModalTitle>
            </ModalHeader>
            <ModalBody style={{
              flex: '1',
              overflow: 'hidden',
              display: 'flex',
              flexDirection: 'column'
            }}>
              {playlists.length === 0 ? (
                <div style={{
                  textAlign: 'center',
                  padding: '2rem',
                  color: 'var(--color-text-secondary)',
                  flex: '1',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <p>No hay playlists disponibles</p>
                </div>
              ) : (
                <div style={{
                  flex: '1',
                  overflowY: 'auto',
                  paddingRight: '0.5rem',
                  marginRight: '-0.5rem'
                }}>
                  {playlists.map((playlist) => {
                    const hasAccess = studentPlaylists.some(sp => sp.playlist_id === playlist.playlist_id);
                    return (
                      <div
                        key={playlist.playlist_id}
                        style={{
                          padding: '1rem',
                          border: '1px solid var(--color-border)',
                          borderRadius: '8px',
                          marginBottom: '1rem',
                          backgroundColor: hasAccess ? '#f8f9fa' : 'var(--color-background)',
                          display: 'flex',
                          alignItems: 'flex-start',
                          gap: '1rem'
                        }}
                      >
                        <div style={{
                          flex: '1',
                          minWidth: '0', // Prevents flex item from overflowing
                          maxWidth: 'calc(100% - 140px)' // Reserve space for button
                        }}>
                          <div style={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: '0.5rem',
                            marginBottom: '0.5rem',
                            flexWrap: 'wrap'
                          }}>
                            <h4 style={{
                              margin: '0',
                              color: 'var(--color-primary)',
                              fontSize: 'var(--font-size-base)',
                              fontWeight: '600',
                              wordBreak: 'break-word'
                            }}>
                              {playlist.title}
                            </h4>
                            {hasAccess && (
                              <span style={{
                                padding: '0.25rem 0.5rem',
                                borderRadius: '12px',
                                fontSize: 'var(--font-size-xs)',
                                backgroundColor: '#d4edda',
                                color: '#155724',
                                whiteSpace: 'nowrap',
                                flexShrink: '0'
                              }}>
                                Acceso Otorgado
                              </span>
                            )}
                          </div>

                          {playlist.description && (
                            <p style={{
                              margin: '0 0 0.75rem 0',
                              fontSize: 'var(--font-size-sm)',
                              color: 'var(--color-text-secondary)',
                              lineHeight: '1.4',
                              wordBreak: 'break-word',
                              overflow: 'hidden',
                              display: '-webkit-box',
                              WebkitLineClamp: '2',
                              WebkitBoxOrient: 'vertical'
                            }}>
                              {playlist.description}
                            </p>
                          )}

                          <div style={{
                            display: 'flex',
                            flexWrap: 'wrap',
                            gap: '1rem',
                            fontSize: 'var(--font-size-sm)',
                            color: 'var(--color-text-secondary)'
                          }}>
                            <span style={{ whiteSpace: 'nowrap' }}>
                              <strong>Videos:</strong> {playlist.video_count || 0}
                            </span>
                            <span style={{ whiteSpace: 'nowrap' }}>
                              <strong>Estado:</strong> {playlist.is_active ? 'Activa' : 'Inactiva'}
                            </span>
                          </div>
                        </div>

                        <div style={{
                          flexShrink: '0',
                          display: 'flex',
                          alignItems: 'flex-start',
                          paddingTop: '0.25rem'
                        }}>
                          <ActionButton
                            onClick={() => handleTogglePlaylistAccess(playlist.playlist_id, hasAccess)}
                            style={{
                              backgroundColor: hasAccess ? '#dc3545' : '#28a745',
                              color: 'white',
                              minWidth: '120px',
                              fontSize: 'var(--font-size-sm)',
                              padding: '0.5rem 0.75rem',
                              whiteSpace: 'nowrap'
                            }}
                          >
                            {hasAccess ? 'Revocar' : 'Otorgar'}
                          </ActionButton>
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </ModalBody>
            <ModalFooter>
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                width: '100%',
                flexWrap: 'wrap',
                gap: '1rem'
              }}>
                <div style={{
                  fontSize: 'var(--font-size-sm)',
                  color: 'var(--color-text-secondary)',
                  flex: '1',
                  minWidth: 'fit-content'
                }}>
                  <strong>{studentPlaylists.length}</strong> de <strong>{playlists.length}</strong> playlists con acceso
                </div>
                <CancelButton
                  onClick={() => setShowPlaylistModal(false)}
                  style={{
                    flexShrink: '0'
                  }}
                >
                  Cerrar
                </CancelButton>
              </div>
            </ModalFooter>
          </ModalContent>
        </Modal>
      )}

      {showMessageModal && selectedStudent && (
        <Modal>
          <ModalContent>
            <ModalHeader>
              <ModalTitle>
                Enviar Mensaje - {selectedStudent.first_name && selectedStudent.last_name
                  ? `${selectedStudent.first_name} ${selectedStudent.last_name}`
                  : selectedStudent.email}
              </ModalTitle>
            </ModalHeader>
            <form onSubmit={handleSendMessageSubmit}>
              <ModalBody>
                <FormGroup>
                  <Label htmlFor="message">Mensaje</Label>
                  <Textarea
                    id="message"
                    value={message}
                    onChange={(e) => setMessage(e.target.value)}
                    placeholder="Escribe tu mensaje aquí..."
                    required
                  />
                </FormGroup>
              </ModalBody>
              <ModalFooter>
                <CancelButton type="button" onClick={() => setShowMessageModal(false)}>
                  Cancelar
                </CancelButton>
                <SubmitButton type="submit">
                  Enviar Mensaje
                </SubmitButton>
              </ModalFooter>
            </form>
          </ModalContent>
        </Modal>
      )}
    </AdminDashboardContainer>
  );
};

export default AdminDashboard;
