const path = require('path');
require('dotenv').config({ path: path.resolve(__dirname, '.env') });

const fs = require('fs');
const db = require('./db');

async function runVideoMetadataMigration() {
    try {
        console.log('🚀 Running video metadata migration...\n');

        // Read and execute the migration
        const migrationPath = path.join(__dirname, 'migrations', 'add-video-metadata.sql');
        const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
        
        await db.query(migrationSQL);
        console.log('✅ Video metadata migration completed successfully\n');

        console.log('📊 Migration summary:');
        console.log('- Created videos table for individual video metadata');
        console.log('- Created playlist_videos table for playlist-video relationships');
        console.log('- Added indexes for performance optimization');
        console.log('- Created triggers for automatic video count updates');
        console.log('- Created view for easy playlist video queries\n');

    } catch (error) {
        console.error('❌ Error running video metadata migration:', error);
        throw error;
    } finally {
        await db.end();
    }
}

// Run the migration if this file is executed directly
if (require.main === module) {
    runVideoMetadataMigration()
        .then(() => {
            console.log('🎉 Video metadata migration completed successfully!');
            process.exit(0);
        })
        .catch((error) => {
            console.error('💥 Migration failed:', error);
            process.exit(1);
        });
}

module.exports = runVideoMetadataMigration;
